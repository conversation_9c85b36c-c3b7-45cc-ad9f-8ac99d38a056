<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DesignV2 - AI Restored Component</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        #app {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* Component styles */
        .design-v2 {
  width: 194px;
  height: 292px;
  border: 2px solid #000000;
  box-sizing: border-box;
}

.layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  width: fit-content;
  height: fit-content;
}

.frame {
  display: flex;
  flex-direction: row;
  gap: 12px;
  width: fit-content;
  height: fit-content;
}

.square {
  width: 75px;
  height: 75px;
  box-sizing: border-box;
}

.square-gray {
  background-color: #D9D9D9;
}

.square-purple {
  background-color: #7B61FF;
}

.group {
  width: 162px;
  height: 77.25px;
  position: relative;
}

.vector-placeholder {
  width: 162px;
  height: 77.25px;
  background-color: #FFFFFF;
  border: 1px solid #E0E0E0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #666;
}

.vector-placeholder::before {
  content: "SVG Vector";
}
    </style>
</head>
<body>
    <div id="app"></div>
    
    <script>
        const { createApp, ref, onMounted, computed, watch, reactive } = Vue;
        
        const ComponentDefinition = {
            template: `<div class="design-v2">
    <div class="layout">
      <!-- Frame 1 -->
      <div class="frame">
        <div class="square square-gray"></div>
        <div class="square square-purple"></div>
      </div>
      
      <!-- Frame 2 -->
      <div class="frame">
        <div class="square square-gray"></div>
        <div class="square square-gray"></div>
      </div>
      
      <!-- Group with SVG -->
      <div class="group">
        <div class="vector-placeholder">
          <!-- SVG placeholder - white background -->
        </div>
      </div>
    </div>
  </div>`,
            setup() {
                
                
                // Return reactive data for template
                return {
                    // Add any reactive data here
                };
            }
        };
        
        createApp(ComponentDefinition).mount('#app');
        
        // Mark as ready for screenshot
        window.componentReady = true;
        console.log('Component mounted and ready');
    </script>
</body>
</html>