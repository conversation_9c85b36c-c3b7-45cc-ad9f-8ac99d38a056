
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DesignV1Component</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: white;
    }
    
.design-v1 {
  /* 🎯 最佳实践：使用Figma JSON的原始尺寸作为内容区域 */
  width: 194px;
  height: 284px;
  border: 2px solid #000000;
  box-sizing: content-box; /* 边框在外部，匹配Figma逻辑 */
  
  /* 可选：优化渲染一致性 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.layout {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  width: 100%;
  height: 100%;
  box-sizing: border-box; /* 内部元素使用border-box便于计算 */
}

.frame-row {
  display: flex;
  flex-direction: row;
  gap: 12px;
}

.square {
  width: 75px;
  height: 75px;
  background-color: #D9D9D9;
}

.rectangle-group {
  width: 162px;
  height: 77.25px;
  position: relative;
}

.rectangle {
  width: 100%;
  height: 100%;
  background-color: #D9D9D9;
  position: relative;
}

.text-in-rectangle {
  position: absolute;
  left: 28px;
  top: 31px;
  width: 105px;
  height: 15px;
  font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.21;
  color: #000000;
  text-align: left;
  
  /* 文本渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

  </style>
</head>
<body>
  
  <div class="design-v1">
    <div class="layout">
      <!-- Frame 1 -->
      <div class="frame-row">
        <div class="square"></div>
        <div class="square"></div>
      </div>
      
      <!-- Rectangle Group -->
      <div class="rectangle-group">
        <div class="rectangle">
          <div class="text-in-rectangle">Some Text In Here</div>
        </div>
      </div>
      
      <!-- Frame 2 -->
      <div class="frame-row">
        <div class="square"></div>
        <div class="square"></div>
      </div>
    </div>
  </div>

</body>
</html>