{"name": "mcp-vue-figma-tools", "version": "1.0.0", "description": "MCP tools for Vue component rendering and Figma integration", "type": "module", "main": "src/server.js", "scripts": {"start": "node src/server.js", "mcp": "node --watch src/server.js", "dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/compiler-sfc": "^3.4.0", "chalk": "^5.3.0", "element-plus": "^2.0.0", "pixelmatch": "^5.3.0", "pngjs": "^7.0.0", "puppeteer": "^21.0.0", "sharp": "^0.34.3", "vite": "^5.0.0", "vue": "^3.4.0", "vue-router": "^4.0.0"}, "keywords": ["mcp", "vue", "figma", "screenshot", "testing"], "author": "Vue Figma Tools", "license": "MIT"}