{"mcpServers": {"figma-restoration-kit": {"command": "node", "args": ["src/server.js"], "cwd": "/Users/<USER>/Documents/work/camscanner-cloud-vue3/mcp-vue-tools", "env": {"PUPPETEER_EXECUTABLE_PATH": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome", "NODE_ENV": "development", "PROJECT_ROOT": "/Users/<USER>/Documents/work/camscanner-cloud-vue3"}}, "figma": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-figma"], "env": {"FIGMA_PERSONAL_ACCESS_TOKEN": "YOUR_FIGMA_TOKEN_HERE"}}}}