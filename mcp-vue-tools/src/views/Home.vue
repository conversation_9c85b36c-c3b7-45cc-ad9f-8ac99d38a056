<template>
  <div class="home">
    <h1>MCP Vue Component Renderer</h1>
    <p>Available Components:</p>
    <ul>
      <li v-for="component in components" :key="component">
        <router-link :to="`/component/${component}`">{{ component }}</router-link>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      components: []
    }
  },
  mounted() {
    // 获取所有可用的组件
    this.loadComponents()
  },
  methods: {
    loadComponents() {
      // 这里可以通过API获取组件列表，暂时硬编码
      this.components = ['RedemptionSuccess']
    }
  }
}
</script>

<style scoped>
.home {
  padding: 20px;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  margin: 10px 0;
}

a {
  color: #2c3e50;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style>
