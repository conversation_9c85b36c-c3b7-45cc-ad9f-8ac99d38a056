<template>
  <div class="design-v1-component">
    <div class="layout-container">
      <!-- Frame 1: First row of squares -->
      <div class="frame-row">
        <div class="square"></div>
        <div class="square"></div>
      </div>
      
      <!-- Rectangle Group: Text container -->
      <div class="rectangle-group">
        <div class="rectangle">
          <div class="text-content">Some Text In Here</div>
        </div>
      </div>
      
      <!-- Frame 2: Second row of squares -->
      <div class="frame-row">
        <div class="square"></div>
        <div class="square"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DesignV1Component',
  data() {
    return {
      componentName: 'Design v1'
    }
  }
}
</script>

<style scoped>
.design-v1-component {
  width: 194px;
  height: 284px;
  border: 2px solid #000000;
  background: #FFFFFF;
  position: relative;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.layout-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.frame-row {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: flex-start;
}

.square {
  width: 75px;
  height: 75px;
  background: #D9D9D9;
  flex-shrink: 0;
}

.rectangle-group {
  width: 162px;
  height: 77.25px;
  position: relative;
  flex-shrink: 0;
}

.rectangle {
  width: 100%;
  height: 100%;
  background: #D9D9D9;
  position: relative;
}

.text-content {
  position: absolute;
  left: 28px;
  top: 31px;
  width: 105px;
  height: 15px;
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 1.21;
  color: #000000;
  text-align: left;
  white-space: nowrap;
}
</style>
