{"mcpServers": {"figma-restoration-kit": {"command": "node", "args": ["src/server.js"], "cwd": "{{PROJECT_ROOT}}/figma-restoration-kit/tools", "env": {"PUPPETEER_EXECUTABLE_PATH": "{{CHROME_PATH}}", "NODE_ENV": "development", "PROJECT_ROOT": "{{PROJECT_ROOT}}"}}, "figma": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-figma"], "env": {"FIGMA_PERSONAL_ACCESS_TOKEN": "{{FIGMA_TOKEN}}"}}}}