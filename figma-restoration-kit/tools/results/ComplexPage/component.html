
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ComplexPage</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: white;
    }
    
.complex-page {
  width: 375px;
  height: 812px;
  background: #1A1F2E;
  position: relative;
  box-sizing: content-box;
  overflow: hidden;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Status Bar */
.status-bar {
  width: 100%;
  height: 44px;
  background: #1A1F2E;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 21px 0 21px;
  box-sizing: border-box;
}

.status-left .time {
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.status-right {
  display: flex;
  align-items: center;
}

.signal-icons {
  display: flex;
  align-items: center;
  gap: 5px;
}

.mobile-signal, .wifi, .battery {
  width: 17px;
  height: 11px;
  background: white;
  opacity: 0.8;
  border-radius: 1px;
}

/* Logo Section */
.logo-section {
  display: flex;
  justify-content: center;
  margin-top: 60px;
  margin-bottom: 120px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #D5B87F 0%, #E8CB91 100%);
  border-radius: 6px;
  position: relative;
}

.logo-text {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.scan-text, .king-text {
  color: white;
  font-size: 18px;
  font-weight: 500;
}

.tm {
  color: white;
  font-size: 8px;
  margin-left: 2px;
}

/* Main Content */
.main-content {
  position: relative;
  margin: 0 14px;
  height: 295px;
}

.member-card-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.member-card {
  position: absolute;
  top: 0;
  left: 31.5px;
  width: 283px;
  height: 212px;
  background: linear-gradient(135deg, #FFF3CE 0%, #F6E1A7 100%);
  border-radius: 8px;
  border: 1px solid #E8CB91;
  z-index: 2;
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 208px;
  height: 151px;
  background: linear-gradient(135deg, #FFFCF2 0%, rgba(255, 252, 242, 0) 100%);
  border-radius: 8px 0 0 0;
}

.card-content {
  position: absolute;
  top: 24px;
  left: 16px;
  width: 172px;
}

.account-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.account-text, .validity-text {
  color: #1A1F2E;
  font-size: 16px;
  line-height: 1.5;
}

.premium-badge {
  position: absolute;
  top: 77px;
  right: 51px;
  width: 94px;
  height: 90px;
  background: linear-gradient(135deg, rgba(232, 203, 145, 0.4) 0%, rgba(232, 203, 145, 0) 100%);
  border: 1px solid rgba(232, 203, 145, 0.5);
  border-radius: 4px;
}

.card-shadow {
  position: absolute;
  top: 53px;
  left: 17.5px;
  width: 311px;
  height: 220px;
  background: #30384E;
  border-radius: 8px;
  z-index: 1;
}

.shadow-left, .shadow-right {
  position: absolute;
  width: 6px;
  height: 145px;
  background: #30384D;
  filter: blur(8px);
  top: 3px;
}

.shadow-left {
  left: 0;
}

.shadow-right {
  right: 0;
}

.shadow-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: #212638;
}

.decoration-left {
  position: absolute;
  top: 246.5px;
  left: 16px;
  width: 44px;
  height: 42px;
  background: #D9D9D9;
  border-radius: 4px;
}

.decoration-right {
  position: absolute;
  top: 170.89px;
  right: 14px;
  width: 98px;
  height: 125px;
  background: #D9D9D9;
  border-radius: 4px;
}

/* Privileges Section */
.privileges-section {
  margin: 20px 24px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.privileges-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.star-left, .star-right {
  width: 20px;
  height: 12px;
  background: #D5B87F;
  border-radius: 2px;
}

.privileges-text {
  color: #D5B87F;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
}

.feature-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 21px;
  justify-content: flex-start;
  width: 100%;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  width: 66px;
}

.feature-icon {
  width: 40px;
  height: 40px;
  background: #333949;
  border: 1px solid #5B6171;
  border-radius: 50%;
  position: relative;
}

.feature-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #FFF1CE 0%, #D5B87F 100%);
  border-radius: 2px;
}

.feature-text {
  color: #D5B87F;
  font-size: 12px;
  text-align: center;
  width: 66px;
}

/* Success Message */
.success-message {
  position: absolute;
  top: 160px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 10;
}

.success-icon {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #F6E2A7 0%, #CCAD74 100%);
  border-radius: 50%;
}

.success-title {
  color: white;
  font-size: 24px;
  font-weight: 700;
  font-family: 'HarmonyOS Sans SC', sans-serif;
}

.success-subtitle {
  position: absolute;
  top: 48px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 16px;
  font-weight: 500;
  text-align: center;
  width: 281px;
}

/* Navigation Bar */
.nav-bar {
  position: absolute;
  bottom: 44px;
  left: 0;
  width: 100%;
  height: 44px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  box-sizing: border-box;
}

.back-icon {
  width: 24px;
  height: 24px;
  background: white;
  border-radius: 2px;
  opacity: 0.8;
}

.action-button {
  width: 87px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border: 0.5px solid #EAEAEA;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-text {
  color: white;
  font-size: 16px;
}

/* Home Indicator */
.home-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.indicator-bar {
  width: 134px;
  height: 5px;
  background: white;
  border-radius: 2.5px;
  opacity: 0.3;
}

  </style>
</head>
<body>
  
  <div class="complex-page">
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-left">
        <div class="time">9:41</div>
      </div>
      <div class="status-right">
        <div class="signal-icons">
          <div class="mobile-signal"></div>
          <div class="wifi"></div>
          <div class="battery"></div>
        </div>
      </div>
    </div>

    <!-- Logo Section -->
    <div class="logo-section">
      <div class="logo-container">
        <div class="logo-icon"></div>
        <div class="logo-text">
          <span class="scan-text">扫描</span>
          <span class="king-text">全能王</span>
          <span class="tm">TM</span>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Member Card -->
      <div class="member-card-container">
        <div class="member-card">
          <div class="card-background"></div>
          <div class="card-decoration-left"></div>
          <div class="card-decoration-right"></div>
          
          <!-- Card Content -->
          <div class="card-content">
            <div class="account-info">
              <div class="account-text">帐户：<br><EMAIL></div>
              <div class="validity-text">有效期至：<br>2026-09-06</div>
            </div>
          </div>
          
          <!-- Premium Badge -->
          <div class="premium-badge">
            <div class="badge-background"></div>
            <div class="badge-icons"></div>
          </div>
        </div>
        
        <!-- Card Shadow/Base -->
        <div class="card-shadow">
          <div class="shadow-left"></div>
          <div class="shadow-right"></div>
          <div class="shadow-bottom"></div>
        </div>
        
        <!-- Decorative Images -->
        <div class="decoration-left">
          <div class="decoration-image-1"></div>
        </div>
        <div class="decoration-right">
          <div class="decoration-image-2"></div>
        </div>
      </div>
    </div>

    <!-- Privileges Section -->
    <div class="privileges-section">
      <div class="privileges-header">
        <div class="star-left"></div>
        <span class="privileges-text">尊享 20+ 高级会员特权</span>
        <div class="star-right"></div>
      </div>
      
      <!-- Feature Grid -->
      <div class="feature-grid">
        <div class="feature-item">
          <div class="feature-icon scan-id"></div>
          <span class="feature-text">证件扫描</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon pdf"></div>
          <span class="feature-text">PDF工具</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon ocr"></div>
          <span class="feature-text">文字识别</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon excel"></div>
          <span class="feature-text">表格识别</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon word"></div>
          <span class="feature-text">Word转换</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon watermark"></div>
          <span class="feature-text">去水印</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon folder"></div>
          <span class="feature-text">文件夹</span>
        </div>
        <div class="feature-item">
          <div class="feature-icon ad-remove"></div>
          <span class="feature-text">去广告</span>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div class="success-message">
      <div class="success-icon"></div>
      <div class="success-title">兑换成功</div>
      <div class="success-subtitle">您已成功兑换高级会员</div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
      <div class="nav-back">
        <div class="back-icon"></div>
      </div>
      <div class="nav-action">
        <div class="action-button">
          <span class="action-text">完成</span>
        </div>
      </div>
    </div>

    <!-- Home Indicator -->
    <div class="home-indicator">
      <div class="indicator-bar"></div>
    </div>
  </div>

</body>
</html>