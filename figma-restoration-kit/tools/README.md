# 🛠️ Figma组件还原工具包

基于MCP (Model Context Protocol) 的Vue组件开发和Figma还原工具集。专注于AI生成的Vue组件验证和像素级对比分析，支持3倍图归一化处理和智能差异定位。

## 📋 核心功能

- **🎨 Vue组件编译渲染**: 编译Vue组件并生成高质量截图
- **🔍 像素级对比分析**: 与Figma设计进行精确的像素对比，支持3倍图归一化
- **🎯 智能元素差异定位**: 基于Figma位置信息精确定位问题元素
- **🛠️ 自动CSS修复建议**: 根据差异分析自动生成针对性修复代码
- **📊 完整质量评估**: 自动生成匹配度报告和改进建议
- **🔄 一站式工作流程**: 从Vue代码到修复建议的端到端自动化

## 📁 目录结构

```
mcp-vue-tools/
├── figma-restoration-toolkit.mjs         # 🎯 核心工具包 (一站式解决方案)
├── compare-pixelmatch-enhanced.mjs       # 🔍 增强版像素对比工具
├── compile-and-render.mjs                # 🖼️ Vue组件编译渲染工具
├── clean-component.mjs                   # 🗑️ 智能组件清理工具
├── show-diff-details.mjs                 # 📊 详细差异分析工具
├── example.mjs                           # 📖 使用示例
├── src/server.js                         # 🛠️ MCP服务器
├── mcp-config.json                       # ⚙️ MCP配置
├── results/                              # 📁 分析结果目录
└── temp/                                 # 📁 临时文件目录
```

## 🚀 快速开始

### 安装依赖

```bash
cd mcp-vue-tools
yarn install  # 或 npm install
```

### 基本使用

```bash
# 使用核心工具包进行完整还原分析
node figma-restoration-toolkit.mjs MyComponent ./MyComponent.vue

# 或者单独使用编译渲染工具
node compile-and-render.mjs MyComponent

# 单独使用像素对比工具
node compare-pixelmatch-enhanced.mjs expected.png actual.png diff.png
```

### MCP服务器配置

Add to your MCP client configuration:

```json
{
  "mcpServers": {
    "vue-figma-tools": {
      "command": "node",
      "args": ["path/to/mcp-vue-tools/src/server.js"],
      "env": {
        "PUPPETEER_EXECUTABLE_PATH": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
      }
    }
  }
}
```

## 🛠️ 核心工具

### 1. figma-restoration-toolkit.mjs ⭐ **主要工具**
一站式Figma组件还原解决方案，包含完整的分析和修复流程。

**功能特性:**
- 🎨 Vue组件编译和渲染
- 🔍 像素级对比分析 (支持3倍图归一化)
- 🎯 智能元素差异定位
- 🛠️ 自动CSS修复建议生成
- 📊 完整质量评估报告

**使用方法:**
```bash
node figma-restoration-toolkit.mjs <component-name> <vue-file-path>

# 示例
node figma-restoration-toolkit.mjs MyButton ./components/MyButton.vue
```

**输出结果:**
- 组件截图 (actual.png)
- 差异对比图 (diff.png)
- 详细分析报告 (restoration-report.json)
- CSS修复建议

### 2. compare-pixelmatch-enhanced.mjs 🔍 **像素对比工具**
增强版像素对比分析工具，支持3倍图归一化和详细差异分析。

**功能特性:**
- 🔢 自动图片尺寸归一化处理
- 📊 网格化差异区域分析
- 🎨 颜色差异统计
- 📈 详细匹配度报告
- 💡 智能修复建议

**使用方法:**
```bash
node compare-pixelmatch-enhanced.mjs <expected-image> <actual-image> <diff-output>

# 示例
node compare-pixelmatch-enhanced.mjs expected.png actual.png diff.png
```

### 3. compile-and-render.mjs 🖼️ **组件渲染工具**
Vue组件编译和截图工具，支持自动元素检测和高质量渲染。

**功能特性:**
- 📝 Vue SFC解析和编译
- 🖼️ 自动组件截图
- 🎯 智能元素选择器
- ⚙️ 可配置渲染参数

**使用方法:**
```bash
node compile-and-render.mjs <component-name> [output-dir]

# 示例
node compile-and-render.mjs MyButton ./results/MyButton
```

### 4. clean-component.mjs 🗑️ **智能清理工具**
智能组件文件清理工具，避免重复生成时文件堆积。

**功能特性:**
- 🗑️ 智能清理组件文件
- 📌 保留重要文件 (expected.png, README.md)
- 🔍 预览模式，安全操作
- 📊 清理统计和文件管理

**使用方法:**
```bash
# 预览清理 (安全模式)
node clean-component.mjs clean MyComponent

# 执行清理
node clean-component.mjs clean MyComponent --execute

# 清理所有组件
node clean-component.mjs clean-all --execute

# 查看清理统计
node clean-component.mjs stats
```

### 5. show-diff-details.mjs 📊 **详细差异分析**
显示完整的差异分析结果，包含位置信息和元素匹配。

**使用方法:**
```bash
node show-diff-details.mjs <component-name>

# 示例
node show-diff-details.mjs MyButton
```

## 📋 工作流程示例

### 完整还原工作流程

**步骤1: 准备Vue组件代码**
```vue
<template>
  <div class="my-button">
    <span class="button-text">Click me</span>
  </div>
</template>

<script>
export default {
  name: 'MyButton'
}
</script>

<style scoped>
.my-button {
  width: 120px;
  height: 40px;
  background: #007bff;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.button-text {
  color: white;
  font-size: 14px;
}
</style>
```

**步骤2: 运行完整分析**
```bash
node figma-restoration-toolkit.mjs MyButton ./MyButton.vue
```

**步骤3: 查看分析结果**
```
🎉 还原工作流程完成！
📊 还原总结:
   匹配度: 97.47%
   质量评级: high
   差异区域: 4个
   修复建议: 2个

💡 建议:
   ✅ 高质量还原，建议直接使用
   🔧 可以应用细节修复建议进一步优化

📁 结果文件: ./results/MyButton/restoration-report.json
```

## 🔧 高级功能

### 坐标归一化处理
工具包自动处理Figma 3倍图与Vue组件的坐标系转换：
- 🔢 自动检测图片缩放比例
- 📐 矩阵归一化坐标转换
- 🎯 精确元素位置匹配

### 智能差异定位
基于Figma位置信息精确定位问题元素：
- 📍 Figma元素位置信息解析
- 🔍 差异区域与元素匹配
- 💡 针对性修复建议生成

### 质量评估体系
- 🏆 **优秀** (99%+): 可直接使用
- ✅ **高质量** (95-99%): 建议微调
- ⚠️ **良好** (90-95%): 需要优化
- ❌ **较差** (<90%): 需要重新实现

## 🌍 环境变量

```bash
# Chrome浏览器路径 (macOS)
export PUPPETEER_EXECUTABLE_PATH="/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

# 开发模式
export NODE_ENV="development"
```

## 📄 License

MIT
