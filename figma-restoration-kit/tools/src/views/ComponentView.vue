<template>
  <div class="component-view">
    <div class="nav-bar">
      <h2>Component: {{ componentName }}</h2>
      <router-link to="/" class="back-link">← Back to Home</router-link>
    </div>
    
    <div 
      id="benchmark-container-for-screenshot"
      class="component-container"
    >
      <component 
        v-if="currentComponent" 
        :is="currentComponent" 
      />
      <div v-else class="error">
        Component "{{ componentName }}" not found
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ComponentView',
  data() {
    return {
      currentComponent: null
    }
  },
  computed: {
    componentName() {
      return this.$route.params.component
    }
  },
  async mounted() {
    await this.loadComponent()
  },
  watch: {
    '$route.params.component': {
      handler: 'loadComponent',
      immediate: true
    }
  },
  methods: {
    async loadComponent() {
      try {
        const componentName = this.componentName
        if (componentName) {
          // 动态导入组件
          const module = await import(`../components/${componentName}/index.vue`)
          this.currentComponent = module.default
        }
      } catch (error) {
        console.error('Failed to load component:', error)
        this.currentComponent = null
      }
    }
  }
}
</script>

<style scoped>
.component-view {
  min-height: 100vh;
  background-color: #f0f2f5;
}

.nav-bar {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-bar h2 {
  margin: 0;
  color: #1a1a1a;
}

.back-link {
  color: #3b82f6;
  text-decoration: none;
}

.back-link:hover {
  text-decoration: underline;
}

.component-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 80px);
  padding: 20px;
  background-color: #fff;
}

.error {
  color: #ef4444;
  font-size: 18px;
  text-align: center;
}
</style>
