import puppeteer from 'puppeteer';
import fs from 'fs/promises';
import chalk from 'chalk';
import {
  getVueServerUrl,
  getDefaultConfig
} from '../utils/path-config.js';

export class RenderComponentTool {
  constructor() {
    this.description = 'Render Vue component in browser using Puppeteer and verify it loads correctly';
    this.inputSchema = {
      type: 'object',
      properties: {
        componentName: {
          type: 'string',
          description: 'Name of the component to render'
        },
        port: {
          type: 'number',
          default: 83,
          description: 'Port where Vue dev server is running'
        },
        timeout: {
          type: 'number',
          default: 15000,
          description: 'Timeout in milliseconds for component loading'
        },
        checkElements: {
          type: 'array',
          items: { type: 'string' },
          description: 'Additional CSS selectors to check for component readiness'
        },
        headless: {
          type: 'boolean',
          default: true,
          description: 'Whether to run browser in headless mode'
        },
        viewport: {
          type: 'object',
          properties: {
            width: { type: 'number', default: 1152 },
            height: { type: 'number', default: 772 }
          },
          description: 'Browser viewport size'
        }
      },
      required: ['componentName']
    };
  }

  async execute(args) {
    const config = getDefaultConfig();
    const {
      componentName,
      port = config.vueServerPort,
      timeout = 15000,
      checkElements = [],
      headless = true,
      viewport = config.defaultViewport
    } = args;

    let browser;
    try {
      const baseUrl = getVueServerUrl(port);
      const componentUrl = getVueServerUrl(port, componentName);

      // First check if dev server is running with simple HTTP request
      const serverStatus = await this.checkServer(baseUrl);
      if (!serverStatus.success) {
        return {
          success: false,
          error: 'Vue dev server is not running',
          suggestion: 'Use vue_dev_server tool to start the server first',
          componentName,
          url: componentUrl
        };
      }

      // Launch browser for real rendering verification with improved Chrome detection
      const chromePaths = [
        process.env.PUPPETEER_EXECUTABLE_PATH,
        '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
        '/usr/bin/google-chrome',
        '/usr/bin/chromium-browser'
      ].filter(Boolean);

      let executablePath = null;
      for (const chromePath of chromePaths) {
        try {
          await fs.access(chromePath);
          executablePath = chromePath;
          break;
        } catch {
          continue;
        }
      }

      const launchOptions = {
        headless: headless ? 'new' : false,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      };

      if (executablePath) {
        launchOptions.executablePath = executablePath;
      }

      browser = await puppeteer.launch(launchOptions);

      const page = await browser.newPage();

      // Set viewport
      await page.setViewport(viewport);

      // Navigate to component page
      console.log(`🌐 Navigating to: ${componentUrl}`);
      await page.goto(componentUrl, {
        waitUntil: 'networkidle2',
        timeout: timeout
      });

      // Wait for Vue app to load
      console.log('⏳ Waiting for Vue app to load...');
      await page.waitForFunction(() => {
        return document.querySelector('#app') && document.querySelector('#app').innerHTML !== '';
      }, { timeout: 5000 });

      // Wait for component element
      const componentSelector = `#benchmark-container-for-screenshot`;
      console.log(`🔍 Looking for component: ${componentSelector}`);

      try {
        await page.waitForSelector(componentSelector, { timeout: timeout });
      } catch (error) {
        return {
          success: false,
          error: `Component element ${componentSelector} not found within ${timeout}ms`,
          componentName,
          url: componentUrl,
          suggestion: 'Check if component is properly registered and the selector is correct'
        };
      }

      // Get component element info
      const componentInfo = await page.evaluate((selector) => {
        const element = document.querySelector(selector);
        if (!element) return null;

        const rect = element.getBoundingClientRect();
        return {
          found: true,
          selector: selector,
          tagName: element.tagName,
          className: element.className,
          id: element.id,
          textContent: element.textContent?.substring(0, 100) || '',
          boundingBox: {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height
          },
          isVisible: rect.width > 0 && rect.height > 0,
          computedStyle: {
            display: window.getComputedStyle(element).display,
            visibility: window.getComputedStyle(element).visibility,
            opacity: window.getComputedStyle(element).opacity
          }
        };
      }, componentSelector);

      // Check additional elements if specified
      const additionalChecks = [];
      if (checkElements.length > 0) {
        for (const selector of checkElements) {
          const elementInfo = await page.evaluate((sel) => {
            const el = document.querySelector(sel);
            return el ? {
              found: true,
              selector: sel,
              tagName: el.tagName,
              textContent: el.textContent?.substring(0, 50) || ''
            } : { found: false, selector: sel };
          }, selector);
          additionalChecks.push(elementInfo);
        }
      }

      // Get page info for debugging
      const pageInfo = await page.evaluate(() => ({
        title: document.title,
        url: window.location.href,
        readyState: document.readyState,
        vueVersion: window.Vue?.version || 'Not detected'
      }));

      return {
        success: true,
        componentName,
        url: componentUrl,
        serverRunning: true,
        componentLoaded: true,
        componentElement: componentInfo,
        additionalChecks,
        pageInfo,
        viewport,
        message: `Component ${componentName} is rendering successfully`,
        readyForScreenshot: componentInfo?.isVisible || false
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        componentName,
        url: `http://localhost:${port}/component/${componentName}`
      };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  async checkServer(baseUrl) {
    try {
      const response = await fetch(baseUrl, {
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      return {
        success: response.ok,
        status: response.status
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}
