import fs from 'fs/promises';
import path from 'path';
import { PNG } from 'pngjs';
import pixelmatch from 'pixelmatch';
import chalk from 'chalk';
import {
  getResultsPath,
  ensureDirectory,
  getDefaultConfig
} from '../utils/path-config.js';

export class CompareImagesTool {
  constructor() {
    this.description = 'Compare two images using pixelmatch and generate diff image';
    this.inputSchema = {
      type: 'object',
      properties: {
        componentName: {
          type: 'string',
          description: 'Name of the component (used to find expected/actual images)'
        },
        expectedPath: {
          type: 'string',
          description: 'Path to expected image (Figma design)'
        },
        actualPath: {
          type: 'string',
          description: 'Path to actual image (screenshot)'
        },
        diffPath: {
          type: 'string',
          description: 'Path where diff image should be saved'
        },
        threshold: {
          type: 'number',
          default: 0.1,
          minimum: 0,
          maximum: 1,
          description: 'Matching threshold (0-1, lower is more strict)'
        },
        includeAA: {
          type: 'boolean',
          default: false,
          description: 'Whether to include anti-aliasing in comparison'
        },
        projectPath: {
          type: 'string',
          default: '/Users/<USER>/Documents/work/camscanner-cloud-vue3',
          description: 'Path to the Vue project'
        }
      }
    };
  }

  async execute(args) {
    const {
      componentName,
      expectedPath,
      actualPath,
      diffPath,
      threshold = 0.1,
      includeAA = false,
      projectPath
    } = args;

    try {
      // Determine file paths if not provided
      const componentDir = path.join(projectPath, 'src/benchmark/components', componentName || '');
      const resultsDir = path.join(projectPath, 'src/benchmark/results', componentName || '');

      const finalExpectedPath = expectedPath || path.join(componentDir, 'expected.png');
      const finalActualPath = actualPath || path.join(resultsDir, 'actual.png');
      const finalDiffPath = diffPath || path.join(resultsDir, 'diff.png');

      // Check if files exist
      const expectedExists = await this.fileExists(finalExpectedPath);
      const actualExists = await this.fileExists(finalActualPath);

      if (!expectedExists) {
        return {
          success: false,
          error: `Expected image not found: ${finalExpectedPath}`,
          expectedPath: finalExpectedPath,
          actualPath: finalActualPath
        };
      }

      if (!actualExists) {
        return {
          success: false,
          error: `Actual image not found: ${finalActualPath}`,
          expectedPath: finalExpectedPath,
          actualPath: finalActualPath
        };
      }

      // Load images
      const expectedBuffer = await fs.readFile(finalExpectedPath);
      const actualBuffer = await fs.readFile(finalActualPath);

      const expectedPng = PNG.sync.read(expectedBuffer);
      const actualPng = PNG.sync.read(actualBuffer);

      // Check dimensions
      if (expectedPng.width !== actualPng.width || expectedPng.height !== actualPng.height) {
        return {
          success: false,
          error: 'Image dimensions do not match',
          expectedDimensions: { width: expectedPng.width, height: expectedPng.height },
          actualDimensions: { width: actualPng.width, height: actualPng.height },
          expectedPath: finalExpectedPath,
          actualPath: finalActualPath
        };
      }

      // Create diff image
      const { width, height } = expectedPng;
      const diffPng = new PNG({ width, height });

      // Compare images
      const numDiffPixels = pixelmatch(
        expectedPng.data,
        actualPng.data,
        diffPng.data,
        width,
        height,
        {
          threshold,
          includeAA,
          alpha: 0.1,
          aaColor: [255, 255, 0],
          diffColor: [255, 0, 0],
          diffColorAlt: [0, 255, 0]
        }
      );

      // Save diff image
      await fs.mkdir(path.dirname(finalDiffPath), { recursive: true });
      await fs.writeFile(finalDiffPath, PNG.sync.write(diffPng));

      // Calculate match percentage
      const totalPixels = width * height;
      const matchPercentage = ((totalPixels - numDiffPixels) / totalPixels) * 100;
      const isMatch = numDiffPixels === 0;
      const isCloseMatch = matchPercentage >= 95; // Consider 95%+ as close match

      return {
        success: true,
        isMatch,
        isCloseMatch,
        numDiffPixels,
        totalPixels,
        matchPercentage: Math.round(matchPercentage * 100) / 100,
        threshold,
        dimensions: { width, height },
        expectedPath: finalExpectedPath,
        actualPath: finalActualPath,
        diffPath: finalDiffPath,
        componentName,
        message: isMatch 
          ? 'Images match perfectly!' 
          : `Images differ by ${numDiffPixels} pixels (${Math.round((100 - matchPercentage) * 100) / 100}% difference)`
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        componentName
      };
    }
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  // Helper method to get image info without comparison
  async getImageInfo(imagePath) {
    try {
      const buffer = await fs.readFile(imagePath);
      const png = PNG.sync.read(buffer);
      
      return {
        success: true,
        path: imagePath,
        width: png.width,
        height: png.height,
        channels: png.channels,
        size: buffer.length
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        path: imagePath
      };
    }
  }

  // Helper method to batch compare multiple components
  async batchCompare(componentNames, options = {}) {
    const results = [];
    
    for (const componentName of componentNames) {
      const result = await this.execute({
        componentName,
        ...options
      });
      results.push(result);
    }

    const summary = {
      total: results.length,
      matches: results.filter(r => r.success && r.isMatch).length,
      closeMatches: results.filter(r => r.success && r.isCloseMatch).length,
      failures: results.filter(r => !r.success).length,
      results
    };

    return summary;
  }
}
