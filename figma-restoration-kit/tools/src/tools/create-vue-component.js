import fs from 'fs/promises';
import path from 'path';
import {
  getMCPToolsPath,
  getComponentPath,
  getResultsPath,
  getVueServerUrl,
  ensureDirectory
} from '../utils/path-config.js';
import chalk from 'chalk';

export class CreateVueComponentTool {
  constructor() {
    this.description = 'Save AI-generated Vue component code to benchmark directory for testing';
    this.inputSchema = {
      type: 'object',
      properties: {
        componentName: {
          type: 'string',
          description: 'Name of the component to create'
        },
        vueCode: {
          type: 'string',
          description: 'Complete Vue component code generated by AI'
        },
        projectPath: {
          type: 'string',
          description: 'Path to the MCP Vue tools project (optional, auto-detected)'
        },
        overwrite: {
          type: 'boolean',
          default: false,
          description: 'Whether to overwrite existing component'
        },
        metadata: {
          type: 'object',
          description: 'Optional metadata about the component (figma URL, etc.)',
          properties: {
            figmaUrl: { type: 'string' },
            description: { type: 'string' },
            createdBy: { type: 'string' }
          }
        }
      },
      required: ['componentName', 'vueCode']
    };
  }

  async execute(args) {
    const {
      componentName,
      vueCode,
      projectPath,
      overwrite = false,
      metadata = {}
    } = args;

    try {
      const componentDir = getComponentPath(componentName, projectPath);
      const componentFile = path.join(componentDir, 'index.vue');
      const metadataFile = path.join(componentDir, 'metadata.json');
      const resultsDir = getResultsPath(componentName, projectPath);

      // Check if component already exists
      const exists = await this.fileExists(componentFile);
      if (exists && !overwrite) {
        return {
          success: false,
          error: `Component ${componentName} already exists. Use overwrite: true to replace it.`,
          componentPath: componentFile
        };
      }

      // Create directories
      await ensureDirectory(componentDir);
      await ensureDirectory(resultsDir);

      // Validate Vue code
      if (!this.isValidVueCode(vueCode)) {
        return {
          success: false,
          error: 'Invalid Vue component code provided',
          componentName
        };
      }

      // Save component file
      await fs.writeFile(componentFile, vueCode, 'utf8');

      // Save metadata if provided
      if (Object.keys(metadata).length > 0) {
        const metadataWithTimestamp = {
          ...metadata,
          createdAt: new Date().toISOString(),
          componentName
        };
        await fs.writeFile(metadataFile, JSON.stringify(metadataWithTimestamp, null, 2), 'utf8');
      }

      return {
        success: true,
        componentName,
        componentPath: componentFile,
        metadataPath: Object.keys(metadata).length > 0 ? metadataFile : null,
        resultsDir,
        testUrl: getVueServerUrl(83, componentName),
        message: `Component ${componentName} saved successfully and ready for testing`
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        componentName
      };
    }
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  isValidVueCode(vueCode) {
    // Basic validation for Vue component code
    if (!vueCode || typeof vueCode !== 'string') {
      return false;
    }

    // Check for required Vue component structure
    const hasTemplate = vueCode.includes('<template>') && vueCode.includes('</template>');
    const hasScript = vueCode.includes('<script') && vueCode.includes('</script>');

    return hasTemplate && hasScript;
  }

}
