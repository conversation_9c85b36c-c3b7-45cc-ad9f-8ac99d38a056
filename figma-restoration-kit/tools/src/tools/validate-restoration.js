import fs from 'fs/promises';
import path from 'path';
import { CreateVueComponentTool } from './create-vue-component.js';
import { TakeScreenshotTool } from './take-screenshot.js';
import { CompareImagesTool } from './compare-images.js';
import {
  getResultsPath,
  getAssetsPath,
  ensureDirectory,
  getDefaultConfig
} from '../utils/path-config.js';

export class ValidateRestorationTool {
  constructor() {
    this.description = 'Complete validation workflow for Figma component restoration';
    this.inputSchema = {
      type: 'object',
      properties: {
        componentName: {
          type: 'string',
          description: 'Name of the component to validate'
        },
        vueCode: {
          type: 'string',
          description: 'AI-generated Vue component code to test'
        },
        expectedImageUrl: {
          type: 'string',
          description: 'URL or path to the expected Figma design image'
        },
        projectPath: {
          type: 'string',
          default: process.cwd(),
          description: 'Path to the MCP Vue tools project'
        },
        port: {
          type: 'number',
          default: 83,
          description: 'Port where Vue dev server is running'
        },
        validationOptions: {
          type: 'object',
          properties: {
            viewport: {
              type: 'object',
              properties: {
                width: { type: 'number', default: 1200 },
                height: { type: 'number', default: 800 }
              }
            },
            screenshotOptions: {
              type: 'object',
              properties: {
                omitBackground: { type: 'boolean', default: true },
                deviceScaleFactor: { type: 'number', default: 2 }
              }
            },
            comparisonThreshold: { type: 'number', default: 0.1 },
            timeout: { type: 'number', default: 15000 }
          }
        }
      },
      required: ['componentName', 'vueCode']
    };
  }

  async execute(args) {
    const {
      componentName,
      vueCode,
      expectedImageUrl,
      projectPath,
      port = 83,
      validationOptions = {}
    } = args;

    const results = {
      componentName,
      timestamp: new Date().toISOString(),
      steps: {},
      success: false,
      summary: {}
    };

    try {
      // Import tools dynamically to avoid circular dependencies
      const { CreateVueComponentTool } = await import('./create-vue-component.js');
      const { RenderComponentTool } = await import('./render-component.js');
      const { TakeScreenshotTool } = await import('./take-screenshot.js');
      const { CompareImagesTool } = await import('./compare-images.js');

      const createTool = new CreateVueComponentTool();
      const renderTool = new RenderComponentTool();
      const screenshotTool = new TakeScreenshotTool();
      const compareTool = new CompareImagesTool();

      // Step 1: Save Vue component
      console.log(`🔧 Step 1: Saving Vue component ${componentName}...`);
      const createResult = await createTool.execute({
        componentName,
        vueCode,
        projectPath,
        overwrite: true,
        metadata: {
          figmaUrl: expectedImageUrl,
          description: 'AI-generated component for validation',
          createdBy: 'MCP Vue Tools'
        }
      });

      results.steps.create = createResult;
      if (!createResult.success) {
        results.summary.error = 'Failed to save component';
        return results;
      }

      // Step 2: Wait for component to be available
      console.log('⏳ Step 2: Waiting for component to load...');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Step 3: Validate rendering
      console.log('🌐 Step 3: Validating component rendering...');
      const renderResult = await renderTool.execute({
        componentName,
        port,
        timeout: validationOptions.timeout || 15000,
        viewport: validationOptions.viewport || { width: 1200, height: 800 },
        headless: true
      });

      results.steps.render = renderResult;
      if (!renderResult.success) {
        results.summary.error = 'Component failed to render';
        return results;
      }

      // Step 4: Take screenshot
      console.log('📸 Step 4: Taking screenshot...');
      const screenshotResult = await screenshotTool.execute({
        componentName,
        projectPath,
        viewport: validationOptions.viewport || { width: 1200, height: 800 },
        screenshotOptions: validationOptions.screenshotOptions || {
          omitBackground: true,
          deviceScaleFactor: 2
        }
      });

      results.steps.screenshot = screenshotResult;
      if (!screenshotResult.success) {
        results.summary.error = 'Failed to take screenshot';
        return results;
      }

      // Step 5: Download expected image if URL provided
      let expectedImagePath = null;
      if (expectedImageUrl) {
        console.log('📥 Step 5: Processing expected image...');
        expectedImagePath = await this.processExpectedImage(
          expectedImageUrl,
          componentName,
          projectPath
        );
        results.steps.expectedImage = { 
          success: !!expectedImagePath, 
          path: expectedImagePath 
        };
      }

      // Step 6: Compare images if expected image is available
      let comparisonResult = null;
      if (expectedImagePath) {
        console.log('🔍 Step 6: Comparing images...');
        comparisonResult = await compareTool.execute({
          componentName,
          expectedPath: expectedImagePath,
          actualPath: screenshotResult.screenshotPath,
          threshold: validationOptions.comparisonThreshold || 0.1,
          projectPath
        });
        results.steps.comparison = comparisonResult;
      }

      // Generate summary
      results.success = true;
      results.summary = {
        componentCreated: createResult.success,
        componentRendered: renderResult.success && renderResult.readyForScreenshot,
        screenshotTaken: screenshotResult.success,
        comparisonAvailable: !!comparisonResult,
        pixelMatch: comparisonResult?.isMatch || null,
        matchPercentage: comparisonResult?.matchPercentage || null,
        files: {
          component: createResult.componentPath,
          screenshot: screenshotResult.screenshotPath,
          expected: expectedImagePath,
          diff: comparisonResult?.diffPath || null
        },
        urls: {
          test: `http://localhost:${port}/component/${componentName}`,
          figma: expectedImageUrl
        }
      };

      return results;

    } catch (error) {
      results.success = false;
      results.summary.error = error.message;
      return results;
    }
  }

  async processExpectedImage(imageUrl, componentName, projectPath) {
    try {
      const componentDir = path.join(process.cwd(), 'results', componentName);
      await fs.mkdir(componentDir, { recursive: true });
      const expectedImagePath = path.join(componentDir, 'expected.png');

      if (imageUrl.startsWith('http')) {
        // Download from URL
        const response = await fetch(imageUrl);
        if (!response.ok) {
          throw new Error(`Failed to download image: ${response.statusText}`);
        }
        const buffer = await response.arrayBuffer();
        await fs.writeFile(expectedImagePath, Buffer.from(buffer));
      } else {
        // Copy from local path
        await fs.copyFile(imageUrl, expectedImagePath);
      }

      return expectedImagePath;
    } catch (error) {
      console.error('Failed to process expected image:', error.message);
      return null;
    }
  }

  // Helper method for batch validation
  async validateMultipleComponents(components) {
    const results = [];
    
    for (const component of components) {
      console.log(`\n🔄 Validating ${component.componentName}...`);
      const result = await this.execute(component);
      results.push(result);
    }

    // Generate batch summary
    const summary = {
      total: results.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      withComparison: results.filter(r => r.summary.comparisonAvailable).length,
      perfectMatches: results.filter(r => r.summary.pixelMatch === true).length,
      results
    };

    return summary;
  }
}
