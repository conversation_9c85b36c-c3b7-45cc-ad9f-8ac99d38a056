# Figma Restoration Kit

A comprehensive toolkit for restoring Figma designs to Vue components using MCP (Model Context Protocol) tools.

## 🚀 Features

- **Figma Design Analysis**: Extract and analyze Figma design data using MCP tools
- **Vue Component Generation**: AI-powered conversion from Figma JSON to Vue components
- **Visual Comparison**: Automated screenshot comparison with pixel-perfect accuracy
- **Testing Framework**: Comprehensive testing tools for component validation
- **Standalone Package**: Complete separation from main projects, works as a submodule

## 📁 Directory Structure

```
figma-restoration-kit/
├── README.md                 # This file
├── docs/                     # Documentation
│   ├── installation.md       # Installation guide
│   ├── workflow.md           # Figma restoration workflow
│   ├── api-reference.md      # MCP tools API reference
│   └── troubleshooting.md    # Common issues and solutions
├── tools/                    # MCP Vue tools
│   ├── src/                  # Source code
│   ├── package.json          # Dependencies
│   └── config/               # Configuration files
├── examples/                 # Example components and workflows
│   ├── components/           # Sample restored components
│   └── workflows/            # Example restoration processes
├── scripts/                  # Installation and setup scripts
│   ├── install.sh            # Main installation script
│   ├── setup-submodule.sh    # Submodule setup script
│   └── test-installation.sh  # Installation verification
├── config/                   # Configuration templates
│   ├── mcp-config.template.json
│   └── cursor-rules.template.md
└── assets/                   # Shared assets and resources
    ├── icons/
    └── images/
```

## 🛠 Installation

### As a Submodule (Recommended)

```bash
# Add as submodule to your project
git submodule add https://github.com/your-org/figma-restoration-kit.git figma-restoration-kit

# Initialize and setup
cd figma-restoration-kit
./scripts/install.sh
```

### Standalone Installation

```bash
# Clone the repository
git clone https://github.com/your-org/figma-restoration-kit.git
cd figma-restoration-kit

# Run installation
./scripts/install.sh
```

## 📖 Quick Start

1. **Setup MCP Configuration**
   ```bash
   # Generate configuration for your project
   ./scripts/setup-submodule.sh
   ```

2. **Start the MCP Server**
   ```bash
   cd tools
   yarn mcp
   ```

3. **Use in Your IDE**
   - Import the generated MCP configuration
   - Restart your IDE (VSCode/Cursor)
   - Access Figma restoration tools via MCP

## 🔧 Usage

### Basic Figma Restoration Workflow

1. **Get Figma Data**: Use MCP tools to extract design data from Figma URL
2. **Generate Component**: AI converts Figma JSON to Vue component code
3. **Test Component**: Automated rendering and screenshot comparison
4. **Validate Results**: Pixel-perfect comparison with original design

### Example Commands

```javascript
// In your IDE with MCP enabled
// 1. Extract Figma data
await getFigmaData({
  fileKey: "your-figma-file-key",
  nodeId: "your-node-id"
});

// 2. Generate and test component
await validateRestoration({
  componentName: "MyComponent",
  vueCode: "generated-vue-code",
  expectedImageUrl: "figma-design-image-url"
});
```

## 📚 Documentation

- [Installation Guide](docs/installation.md) - Detailed setup instructions
- [Workflow Documentation](docs/workflow.md) - Complete restoration process
- [API Reference](docs/api-reference.md) - MCP tools documentation
- [Troubleshooting](docs/troubleshooting.md) - Common issues and solutions

## 🧪 Testing

```bash
# Run installation tests
./scripts/test-installation.sh

# Test MCP tools functionality
cd tools
yarn test
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🆘 Support

- Check [troubleshooting guide](docs/troubleshooting.md)
- Open an issue on GitHub
- Review example workflows in `examples/`

---

**Note**: This kit is designed to be completely independent and can be integrated into any Vue project as a submodule without affecting the main project structure.
